#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>};
use solana_entry::entry::Entry;

#[macro_use]
extern crate napi_derive;

mod types;
mod utils;

use types::ParsedEntry;
use utils::convert_entries_to_parsed;

#[napi]
pub fn decode_entries(bytes: <PERSON>s<PERSON><PERSON><PERSON>) -> Result<ParsedEntry> {
  let entries: Vec<Entry> = bincode::deserialize(&bytes.into_value()?)
    .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))?;

  Ok(convert_entries_to_parsed(entries))
}
