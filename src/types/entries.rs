use crate::types::{transactions::VersionedTransaction, Hash};
use napi_derive::napi;

#[napi(object)]
#[derive(Debug, Clone)]
pub struct Entry {
  pub num_hashes: f64,
  pub hash: Hash,
  pub transactions: Vec<VersionedTransaction>,
}

impl Entry {
  pub fn from_solana_entry(entry: &solana_entry::entry::Entry) -> Self {
    Self {
      num_hashes: entry.num_hashes as f64,
      hash: Hash::from_solana_hash(&entry.hash),
      transactions: entry
        .transactions
        .iter()
        .map(|tx| VersionedTransaction::from_solana_versioned_transaction(tx))
        .collect(),
    }
  }
}

#[napi(object)]
#[derive(Debug, Clone)]
pub struct ParsedEntry {
  pub entries: Vec<Entry>,
}
