use crate::types::{
  messages::{LegacyMessage, V0Message},
  Signature,
};
use napi_derive::napi;

#[napi(object)]
#[derive(Debug, <PERSON><PERSON>)]
pub struct VersionedMessage {
  pub message_type: String,
  pub legacy: Option<LegacyMessage>,
  pub v0: Option<V0Message>,
}

impl VersionedMessage {
  pub fn from_solana_versioned_message(message: &solana_message::VersionedMessage) -> Self {
    match message {
      solana_message::VersionedMessage::Legacy(legacy_msg) => Self {
        message_type: "Legacy".to_string(),
        legacy: Some(LegacyMessage::from_solana_message(legacy_msg)),
        v0: None,
      },
      solana_message::VersionedMessage::V0(v0_msg) => Self {
        message_type: "V0".to_string(),
        legacy: None,
        v0: Some(V0Message::from_solana_v0_message(v0_msg)),
      },
    }
  }
}

#[napi(object)]
#[derive(Debug, <PERSON>lone)]
pub struct VersionedTransaction {
  pub signatures: Vec<Signature>,
  pub message: VersionedMessage,
}

impl VersionedTransaction {
  pub fn from_solana_versioned_transaction(
    transaction: &solana_transaction::versioned::VersionedTransaction,
  ) -> Self {
    Self {
      signatures: transaction
        .signatures
        .iter()
        .map(|sig| Signature::from_solana_signature(sig))
        .collect(),
      message: VersionedMessage::from_solana_versioned_message(&transaction.message),
    }
  }
}
