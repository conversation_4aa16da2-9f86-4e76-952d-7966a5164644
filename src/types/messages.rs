use crate::types::{Hash, Pubkey};
use napi_derive::napi;

#[napi(object)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct MessageHeader {
  pub num_required_signatures: u32,
  pub num_readonly_signed_accounts: u32,
  pub num_readonly_unsigned_accounts: u32,
}

impl MessageHeader {
  pub fn from_solana_header(header: &solana_message::MessageHeader) -> Self {
    Self {
      num_required_signatures: header.num_required_signatures as u32,
      num_readonly_signed_accounts: header.num_readonly_signed_accounts as u32,
      num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u32,
    }
  }
}

#[napi(object)]
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct CompiledInstruction {
  pub program_id_index: u32,
  pub accounts: Vec<u8>,
  pub data: Vec<u8>,
}

impl CompiledInstruction {
  pub fn from_solana_instruction(
    instruction: &solana_message::compiled_instruction::CompiledInstruction,
  ) -> Self {
    Self {
      program_id_index: instruction.program_id_index as u32,
      accounts: instruction.accounts.clone(),
      data: instruction.data.clone(),
    }
  }
}

#[napi(object)]
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct MessageAddressTableLookup {
  pub account_key: Pubkey,
  pub writable_indexes: Vec<u8>,
  pub readonly_indexes: Vec<u8>,
}

impl MessageAddressTableLookup {
  pub fn from_solana_lookup(lookup: &solana_message::v0::MessageAddressTableLookup) -> Self {
    Self {
      account_key: Pubkey::from_solana_pubkey(&lookup.account_key),
      writable_indexes: lookup.writable_indexes.clone(),
      readonly_indexes: lookup.readonly_indexes.clone(),
    }
  }
}

#[napi(object)]
#[derive(Debug, Clone)]
pub struct LegacyMessage {
  pub header: MessageHeader,
  pub account_keys: Vec<Pubkey>,
  pub recent_blockhash: Hash,
  pub instructions: Vec<CompiledInstruction>,
}

impl LegacyMessage {
  pub fn from_solana_message(message: &solana_message::Message) -> Self {
    Self {
      header: MessageHeader::from_solana_header(&message.header),
      account_keys: message
        .account_keys
        .iter()
        .map(|key| Pubkey::from_solana_pubkey(key))
        .collect(),
      recent_blockhash: Hash::from_solana_hash(&message.recent_blockhash),
      instructions: message
        .instructions
        .iter()
        .map(|inst| CompiledInstruction::from_solana_instruction(inst))
        .collect(),
    }
  }
}

#[napi(object)]
#[derive(Debug, Clone)]
pub struct V0Message {
  pub header: MessageHeader,
  pub account_keys: Vec<Pubkey>,
  pub recent_blockhash: Hash,
  pub instructions: Vec<CompiledInstruction>,
  pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

impl V0Message {
  pub fn from_solana_v0_message(message: &solana_message::v0::Message) -> Self {
    Self {
      header: MessageHeader::from_solana_header(&message.header),
      account_keys: message
        .account_keys
        .iter()
        .map(|key| Pubkey::from_solana_pubkey(key))
        .collect(),
      recent_blockhash: Hash::from_solana_hash(&message.recent_blockhash),
      instructions: message
        .instructions
        .iter()
        .map(|inst| CompiledInstruction::from_solana_instruction(inst))
        .collect(),
      address_table_lookups: message
        .address_table_lookups
        .iter()
        .map(|lookup| MessageAddressTableLookup::from_solana_lookup(lookup))
        .collect(),
    }
  }
}
