use crate::types::*;
use solana_entry::entry::Entry;
use solana_hash::Hash;
use solana_pubkey::Pubkey;
use solana_signature::Signature;
use solana_transaction::versioned::VersionedTransaction;
use solana_message::{Message, VersionedMessage, MessageHeader};
use solana_message::compiled_instruction::CompiledInstruction;
use solana_message::v0::{Message as V0Message, MessageAddressTableLookup};

pub fn convert_parsed_entry_to_solana(parsed_entry: &ParsedEntry) -> Vec<Entry> {
    parsed_entry.entries
        .iter()
        .map(|entry| convert_entry_to_solana(entry))
        .collect()
}

fn convert_entry_to_solana(entry: &crate::types::Entry) -> Entry {
    Entry {
        num_hashes: entry.num_hashes as u64,
        hash: convert_hash_to_solana(&entry.hash),
        transactions: entry.transactions
            .iter()
            .map(|tx| convert_versioned_transaction_to_solana(tx))
            .collect(),
    }
}

fn convert_hash_to_solana(hash: &crate::types::Hash) -> Hash {
    let mut bytes = [0u8; 32];
    if hash.bytes.len() == 32 {
        bytes.copy_from_slice(&hash.bytes);
    } else {
        panic!("Invalid hash length: expected 32 bytes, got {}", hash.bytes.len());
    }
    Hash::new_from_array(bytes)
}

fn convert_pubkey_to_solana(pubkey: &crate::types::Pubkey) -> Pubkey {
    let mut bytes = [0u8; 32];
    if pubkey.bytes.len() == 32 {
        bytes.copy_from_slice(&pubkey.bytes);
    } else {
        panic!("Invalid pubkey length: expected 32 bytes, got {}", pubkey.bytes.len());
    }
    Pubkey::new_from_array(bytes)
}

fn convert_signature_to_solana(signature: &crate::types::Signature) -> Signature {
    let mut bytes = [0u8; 64];
    if signature.bytes.len() == 64 {
        bytes.copy_from_slice(&signature.bytes);
    } else {
        panic!("Invalid signature length: expected 64 bytes, got {}", signature.bytes.len());
    }
    Signature::from(bytes)
}

fn convert_versioned_transaction_to_solana(tx: &crate::types::VersionedTransaction) -> VersionedTransaction {
    let signatures: Vec<Signature> = tx.signatures
        .iter()
        .map(|sig| convert_signature_to_solana(sig))
        .collect();
    
    let message = convert_versioned_message_to_solana(&tx.message);
    
    VersionedTransaction {
        signatures,
        message,
    }
}

fn convert_versioned_message_to_solana(msg: &crate::types::VersionedMessage) -> VersionedMessage {
    match msg.message_type.as_str() {
        "Legacy" => {
            if let Some(legacy_msg) = &msg.legacy {
                VersionedMessage::Legacy(convert_legacy_message_to_solana(legacy_msg))
            } else {
                panic!("Legacy message type specified but legacy field is None");
            }
        },
        "V0" => {
            if let Some(v0_msg) = &msg.v0 {
                VersionedMessage::V0(convert_v0_message_to_solana(v0_msg))
            } else {
                panic!("V0 message type specified but v0 field is None");
            }
        },
        _ => panic!("Unknown message type: {}", msg.message_type),
    }
}

fn convert_legacy_message_to_solana(msg: &crate::types::LegacyMessage) -> Message {
    let header = convert_message_header_to_solana(&msg.header);
    
    let account_keys: Vec<Pubkey> = msg.account_keys
        .iter()
        .map(|key| convert_pubkey_to_solana(key))
        .collect();
    
    let recent_blockhash = convert_hash_to_solana(&msg.recent_blockhash);
    
    let instructions: Vec<CompiledInstruction> = msg.instructions
        .iter()
        .map(|inst| convert_compiled_instruction_to_solana(inst))
        .collect();
    
    Message {
        header,
        account_keys,
        recent_blockhash,
        instructions,
    }
}

fn convert_v0_message_to_solana(msg: &crate::types::V0Message) -> V0Message {
    let header = convert_message_header_to_solana(&msg.header);
    
    let account_keys: Vec<Pubkey> = msg.account_keys
        .iter()
        .map(|key| convert_pubkey_to_solana(key))
        .collect();
    
    let recent_blockhash = convert_hash_to_solana(&msg.recent_blockhash);
    
    let instructions: Vec<CompiledInstruction> = msg.instructions
        .iter()
        .map(|inst| convert_compiled_instruction_to_solana(inst))
        .collect();
    
    let address_table_lookups: Vec<MessageAddressTableLookup> = msg.address_table_lookups
        .iter()
        .map(|lookup| convert_address_table_lookup_to_solana(lookup))
        .collect();
    
    V0Message {
        header,
        account_keys,
        recent_blockhash,
        instructions,
        address_table_lookups,
    }
}

fn convert_message_header_to_solana(header: &crate::types::MessageHeader) -> MessageHeader {
    MessageHeader {
        num_required_signatures: header.num_required_signatures as u8,
        num_readonly_signed_accounts: header.num_readonly_signed_accounts as u8,
        num_readonly_unsigned_accounts: header.num_readonly_unsigned_accounts as u8,
    }
}

fn convert_compiled_instruction_to_solana(inst: &crate::types::CompiledInstruction) -> CompiledInstruction {
    CompiledInstruction {
        program_id_index: inst.program_id_index as u8,
        accounts: inst.accounts.clone(),
        data: inst.data.clone(),
    }
}

fn convert_address_table_lookup_to_solana(lookup: &crate::types::MessageAddressTableLookup) -> MessageAddressTableLookup {
    MessageAddressTableLookup {
        account_key: convert_pubkey_to_solana(&lookup.account_key),
        writable_indexes: lookup.writable_indexes.clone(),
        readonly_indexes: lookup.readonly_indexes.clone(),
    }
}
