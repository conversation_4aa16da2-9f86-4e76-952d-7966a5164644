/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

export interface Entry {
  numHashes: number
  hash: Hash
  transactions: Array<VersionedTransaction>
}
export interface ParsedEntry {
  entries: Array<Entry>
}
export interface Hash {
  bytes: Array<number>
}
export interface MessageHeader {
  numRequiredSignatures: number
  numReadonlySignedAccounts: number
  numReadonlyUnsignedAccounts: number
}
export interface CompiledInstruction {
  programIdIndex: number
  accounts: Array<number>
  data: Array<number>
}
export interface MessageAddressTableLookup {
  accountKey: Pubkey
  writableIndexes: Array<number>
  readonlyIndexes: Array<number>
}
export interface LegacyMessage {
  header: MessageHeader
  accountKeys: Array<Pubkey>
  recentBlockhash: Hash
  instructions: Array<CompiledInstruction>
}
export interface V0Message {
  header: MessageHeader
  accountKeys: Array<Pubkey>
  recentBlockhash: Hash
  instructions: Array<CompiledInstruction>
  addressTableLookups: Array<MessageAddressTableLookup>
}
export interface Pubkey {
  bytes: Array<number>
}
export interface Signature {
  bytes: Array<number>
}
export interface VersionedMessage {
  messageType: string
  legacy?: LegacyMessage
  v0?: V0Message
}
export interface VersionedTransaction {
  signatures: Array<Signature>
  message: VersionedMessage
}
export declare function decodeEntries(bytes: Buffer): ParsedEntry
