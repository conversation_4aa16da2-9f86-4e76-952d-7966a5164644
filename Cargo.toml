[package]
edition = "2021"
name = "shredstream-decoder"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = { version = "2.12.2", default-features = false, features = ["napi8"] }
napi-derive = "2.12.2"
serde = { version = "1.0.219", features = ["derive"] }
solana-entry = "2.2.7"
bincode = "1.3.3"
solana-hash = "2.2.1"
solana-signature = "2.2.1"
solana-pubkey = "2.2.1"
solana-message = "2.2.1"
solana-transaction = "2.2.1"

[build-dependencies]
napi-build = "2.0.1"

[profile.release]
lto = true
strip = "symbols"
