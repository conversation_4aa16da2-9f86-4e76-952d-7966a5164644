use solana_entry::entry::Entry;
use solana_hash::Hash;
use solana_pubkey::Pubkey;
use solana_signature::Signature;
use solana_transaction::versioned::VersionedTransaction;
use solana_message::{Message, VersionedMessage, MessageHeader};
use solana_message::compiled_instruction::CompiledInstruction;
use solana_message::v0::{Message as V0Message, MessageAddressTableLookup};

pub fn assert_entries_equal(entry1: &Entry, entry2: &Entry) {
    assert_eq!(entry1.num_hashes, entry2.num_hashes, 
        "Entry num_hashes mismatch: {} vs {}", entry1.num_hashes, entry2.num_hashes);
    
    assert_hashes_equal(&entry1.hash, &entry2.hash);
    
    assert_eq!(entry1.transactions.len(), entry2.transactions.len(),
        "Entry transactions length mismatch: {} vs {}", 
        entry1.transactions.len(), entry2.transactions.len());
    
    for (i, (tx1, tx2)) in entry1.transactions.iter().zip(entry2.transactions.iter()).enumerate() {
        println!("  Comparing transaction {}", i);
        assert_versioned_transactions_equal(tx1, tx2);
    }
}

fn assert_hashes_equal(hash1: &Hash, hash2: &Hash) {
    let bytes1 = hash1.to_bytes();
    let bytes2 = hash2.to_bytes();
    assert_eq!(bytes1, bytes2, 
        "Hash mismatch: {:?} vs {:?}", bytes1, bytes2);
}

fn assert_pubkeys_equal(pubkey1: &Pubkey, pubkey2: &Pubkey) {
    let bytes1 = pubkey1.to_bytes();
    let bytes2 = pubkey2.to_bytes();
    assert_eq!(bytes1, bytes2, 
        "Pubkey mismatch: {:?} vs {:?}", bytes1, bytes2);
}

fn assert_signatures_equal(sig1: &Signature, sig2: &Signature) {
    let bytes1 = sig1.as_ref();
    let bytes2 = sig2.as_ref();
    assert_eq!(bytes1, bytes2, 
        "Signature mismatch: {:?} vs {:?}", bytes1, bytes2);
}

fn assert_versioned_transactions_equal(tx1: &VersionedTransaction, tx2: &VersionedTransaction) {
    assert_eq!(tx1.signatures.len(), tx2.signatures.len(),
        "Transaction signatures length mismatch: {} vs {}", 
        tx1.signatures.len(), tx2.signatures.len());
    
    for (i, (sig1, sig2)) in tx1.signatures.iter().zip(tx2.signatures.iter()).enumerate() {
        println!("    Comparing signature {}", i);
        assert_signatures_equal(sig1, sig2);
    }
    
    assert_versioned_messages_equal(&tx1.message, &tx2.message);
}

fn assert_versioned_messages_equal(msg1: &VersionedMessage, msg2: &VersionedMessage) {
    match (msg1, msg2) {
        (VersionedMessage::Legacy(legacy1), VersionedMessage::Legacy(legacy2)) => {
            assert_legacy_messages_equal(legacy1, legacy2);
        },
        (VersionedMessage::V0(v0_1), VersionedMessage::V0(v0_2)) => {
            assert_v0_messages_equal(v0_1, v0_2);
        },
        _ => panic!("Message type mismatch: {:?} vs {:?}", 
            discriminant_name(msg1), discriminant_name(msg2)),
    }
}

fn assert_legacy_messages_equal(msg1: &Message, msg2: &Message) {
    assert_message_headers_equal(&msg1.header, &msg2.header);
    
    assert_eq!(msg1.account_keys.len(), msg2.account_keys.len(),
        "Legacy message account_keys length mismatch: {} vs {}", 
        msg1.account_keys.len(), msg2.account_keys.len());
    
    for (i, (key1, key2)) in msg1.account_keys.iter().zip(msg2.account_keys.iter()).enumerate() {
        println!("      Comparing account key {}", i);
        assert_pubkeys_equal(key1, key2);
    }
    
    assert_hashes_equal(&msg1.recent_blockhash, &msg2.recent_blockhash);
    
    assert_eq!(msg1.instructions.len(), msg2.instructions.len(),
        "Legacy message instructions length mismatch: {} vs {}", 
        msg1.instructions.len(), msg2.instructions.len());
    
    for (i, (inst1, inst2)) in msg1.instructions.iter().zip(msg2.instructions.iter()).enumerate() {
        println!("      Comparing instruction {}", i);
        assert_compiled_instructions_equal(inst1, inst2);
    }
}

fn assert_v0_messages_equal(msg1: &V0Message, msg2: &V0Message) {
    assert_message_headers_equal(&msg1.header, &msg2.header);
    
    assert_eq!(msg1.account_keys.len(), msg2.account_keys.len(),
        "V0 message account_keys length mismatch: {} vs {}", 
        msg1.account_keys.len(), msg2.account_keys.len());
    
    for (i, (key1, key2)) in msg1.account_keys.iter().zip(msg2.account_keys.iter()).enumerate() {
        println!("      Comparing account key {}", i);
        assert_pubkeys_equal(key1, key2);
    }
    
    assert_hashes_equal(&msg1.recent_blockhash, &msg2.recent_blockhash);
    
    assert_eq!(msg1.instructions.len(), msg2.instructions.len(),
        "V0 message instructions length mismatch: {} vs {}", 
        msg1.instructions.len(), msg2.instructions.len());
    
    for (i, (inst1, inst2)) in msg1.instructions.iter().zip(msg2.instructions.iter()).enumerate() {
        println!("      Comparing instruction {}", i);
        assert_compiled_instructions_equal(inst1, inst2);
    }
    
    assert_eq!(msg1.address_table_lookups.len(), msg2.address_table_lookups.len(),
        "V0 message address_table_lookups length mismatch: {} vs {}", 
        msg1.address_table_lookups.len(), msg2.address_table_lookups.len());
    
    for (i, (lookup1, lookup2)) in msg1.address_table_lookups.iter().zip(msg2.address_table_lookups.iter()).enumerate() {
        println!("      Comparing address table lookup {}", i);
        assert_address_table_lookups_equal(lookup1, lookup2);
    }
}

fn assert_message_headers_equal(header1: &MessageHeader, header2: &MessageHeader) {
    assert_eq!(header1.num_required_signatures, header2.num_required_signatures,
        "MessageHeader num_required_signatures mismatch: {} vs {}", 
        header1.num_required_signatures, header2.num_required_signatures);
    
    assert_eq!(header1.num_readonly_signed_accounts, header2.num_readonly_signed_accounts,
        "MessageHeader num_readonly_signed_accounts mismatch: {} vs {}", 
        header1.num_readonly_signed_accounts, header2.num_readonly_signed_accounts);
    
    assert_eq!(header1.num_readonly_unsigned_accounts, header2.num_readonly_unsigned_accounts,
        "MessageHeader num_readonly_unsigned_accounts mismatch: {} vs {}", 
        header1.num_readonly_unsigned_accounts, header2.num_readonly_unsigned_accounts);
}

fn assert_compiled_instructions_equal(inst1: &CompiledInstruction, inst2: &CompiledInstruction) {
    assert_eq!(inst1.program_id_index, inst2.program_id_index,
        "CompiledInstruction program_id_index mismatch: {} vs {}", 
        inst1.program_id_index, inst2.program_id_index);
    
    assert_eq!(inst1.accounts, inst2.accounts,
        "CompiledInstruction accounts mismatch: {:?} vs {:?}", 
        inst1.accounts, inst2.accounts);
    
    assert_eq!(inst1.data, inst2.data,
        "CompiledInstruction data mismatch: {:?} vs {:?}", 
        inst1.data, inst2.data);
}

fn assert_address_table_lookups_equal(lookup1: &MessageAddressTableLookup, lookup2: &MessageAddressTableLookup) {
    assert_pubkeys_equal(&lookup1.account_key, &lookup2.account_key);
    
    assert_eq!(lookup1.writable_indexes, lookup2.writable_indexes,
        "MessageAddressTableLookup writable_indexes mismatch: {:?} vs {:?}", 
        lookup1.writable_indexes, lookup2.writable_indexes);
    
    assert_eq!(lookup1.readonly_indexes, lookup2.readonly_indexes,
        "MessageAddressTableLookup readonly_indexes mismatch: {:?} vs {:?}", 
        lookup1.readonly_indexes, lookup2.readonly_indexes);
}

fn discriminant_name(msg: &VersionedMessage) -> &'static str {
    match msg {
        VersionedMessage::Legacy(_) => "Legacy",
        VersionedMessage::V0(_) => "V0",
    }
}
