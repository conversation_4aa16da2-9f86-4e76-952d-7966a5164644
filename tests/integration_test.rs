use napi::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Result as <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::fs;
use std::path::Path;
use tempfile::TempDir;

use shredstream_decoder::{decode_entries, types::ParsedEntry};
use solana_entry::entry::Entry;
use solana_hash::Hash;
use solana_message::{Message, VersionedMessage};
use solana_pubkey::Pubkey;
use solana_signature::Signature;
use solana_transaction::versioned::VersionedTransaction;

mod comparison_utils;
mod reverse_conversions;
mod test_data_generator;

use comparison_utils::*;
use reverse_conversions::*;
use test_data_generator::*;

#[test]
fn test_empty_entry_round_trip() {
  env_logger::init();

  // Generate test data
  let original_entries = generate_empty_entries(3);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  // Test round-trip conversion
  test_round_trip_conversion(&serialized_data, &original_entries);
}

#[test]
fn test_single_transaction_entry_round_trip() {
  env_logger::init();

  // Generate test data with single transaction
  let original_entries = generate_single_transaction_entries(2);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  // Test round-trip conversion
  test_round_trip_conversion(&serialized_data, &original_entries);
}

#[test]
fn test_multiple_transaction_entry_round_trip() {
  env_logger::init();

  // Generate test data with multiple transactions
  let original_entries = generate_multiple_transaction_entries(1, 5);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  // Test round-trip conversion
  test_round_trip_conversion(&serialized_data, &original_entries);
}

#[test]
fn test_legacy_message_round_trip() {
  env_logger::init();

  // Generate test data with legacy messages
  let original_entries = generate_legacy_message_entries(2);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  // Test round-trip conversion
  test_round_trip_conversion(&serialized_data, &original_entries);
}

#[test]
fn test_v0_message_round_trip() {
  env_logger::init();

  // Generate test data with V0 messages
  let original_entries = generate_v0_message_entries(2);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  // Test round-trip conversion
  test_round_trip_conversion(&serialized_data, &original_entries);
}

#[test]
fn test_large_dataset_performance() {
  env_logger::init();

  // Generate large dataset
  let original_entries = generate_mixed_entries(100);
  let serialized_data = bincode::serialize(&original_entries).unwrap();

  let start_time = std::time::Instant::now();
  test_round_trip_conversion(&serialized_data, &original_entries);
  let duration = start_time.elapsed();

  println!("Large dataset test completed in: {:?}", duration);
  assert!(
    duration.as_secs() < 10,
    "Performance test should complete within 10 seconds"
  );
}

fn test_round_trip_conversion(serialized_data: &[u8], original_entries: &[Entry]) {
  // Step 1: Test original deserialization
  let deserialized_original: Vec<Entry> =
    bincode::deserialize(serialized_data).expect("Failed to deserialize original data");

  // Verify original deserialization
  assert_eq!(original_entries.len(), deserialized_original.len());
  for (orig, deser) in original_entries.iter().zip(deserialized_original.iter()) {
    assert_entries_equal(orig, deser);
  }

  // Step 2: Test wrapper conversion using decode_entries
  let js_buffer_mock = MockJsBuffer::new(serialized_data.to_vec());
  let parsed_entry = decode_entries(js_buffer_mock).expect("Failed to decode entries with wrapper");

  // Step 3: Test reverse conversion
  let round_trip_entries = convert_parsed_entry_to_solana(&parsed_entry);

  // Step 4: Verify data integrity
  assert_eq!(original_entries.len(), round_trip_entries.len());

  for (i, (original, round_trip)) in original_entries
    .iter()
    .zip(round_trip_entries.iter())
    .enumerate()
  {
    println!("Comparing entry {}", i);
    assert_entries_equal(original, round_trip);
  }

  println!(
    "Round-trip test passed for {} entries",
    original_entries.len()
  );
}

// Mock JsBuffer for testing
struct MockJsBuffer {
  data: Vec<u8>,
}

impl MockJsBuffer {
  fn new(data: Vec<u8>) -> Self {
    Self { data }
  }
}

impl JsBuffer for MockJsBuffer {
  fn into_value(self) -> NapiResult<Vec<u8>> {
    Ok(self.data)
  }
}
