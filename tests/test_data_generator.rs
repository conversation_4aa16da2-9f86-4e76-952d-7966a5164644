use rand::Rng;
use solana_entry::entry::Entry;
use solana_hash::Hash;
use solana_pubkey::Pubkey;
use solana_signature::Signature;
use solana_transaction::versioned::VersionedTransaction;
use solana_message::{Message, VersionedMessage, MessageHeader};
use solana_message::compiled_instruction::CompiledInstruction;
use solana_message::v0::{Message as V0Message, MessageAddressTableLookup};

pub fn generate_empty_entries(count: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for _ in 0..count {
        let entry = Entry {
            num_hashes: rng.gen_range(1..1000),
            hash: generate_random_hash(),
            transactions: vec![], // Empty transactions for tick entries
        };
        entries.push(entry);
    }
    
    entries
}

pub fn generate_single_transaction_entries(count: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for _ in 0..count {
        let transaction = generate_legacy_transaction();
        let entry = Entry {
            num_hashes: rng.gen_range(1..1000),
            hash: generate_random_hash(),
            transactions: vec![transaction],
        };
        entries.push(entry);
    }
    
    entries
}

pub fn generate_multiple_transaction_entries(entry_count: usize, tx_per_entry: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for _ in 0..entry_count {
        let mut transactions = Vec::new();
        for _ in 0..tx_per_entry {
            transactions.push(generate_legacy_transaction());
        }
        
        let entry = Entry {
            num_hashes: rng.gen_range(1..1000),
            hash: generate_random_hash(),
            transactions,
        };
        entries.push(entry);
    }
    
    entries
}

pub fn generate_legacy_message_entries(count: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for _ in 0..count {
        let transaction = generate_legacy_transaction();
        let entry = Entry {
            num_hashes: rng.gen_range(1..1000),
            hash: generate_random_hash(),
            transactions: vec![transaction],
        };
        entries.push(entry);
    }
    
    entries
}

pub fn generate_v0_message_entries(count: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for _ in 0..count {
        let transaction = generate_v0_transaction();
        let entry = Entry {
            num_hashes: rng.gen_range(1..1000),
            hash: generate_random_hash(),
            transactions: vec![transaction],
        };
        entries.push(entry);
    }
    
    entries
}

pub fn generate_mixed_entries(count: usize) -> Vec<Entry> {
    let mut entries = Vec::new();
    let mut rng = rand::thread_rng();
    
    for i in 0..count {
        let entry = match i % 4 {
            0 => {
                // Empty entry
                Entry {
                    num_hashes: rng.gen_range(1..1000),
                    hash: generate_random_hash(),
                    transactions: vec![],
                }
            },
            1 => {
                // Single legacy transaction
                Entry {
                    num_hashes: rng.gen_range(1..1000),
                    hash: generate_random_hash(),
                    transactions: vec![generate_legacy_transaction()],
                }
            },
            2 => {
                // Single V0 transaction
                Entry {
                    num_hashes: rng.gen_range(1..1000),
                    hash: generate_random_hash(),
                    transactions: vec![generate_v0_transaction()],
                }
            },
            3 => {
                // Multiple transactions
                let tx_count = rng.gen_range(2..5);
                let mut transactions = Vec::new();
                for _ in 0..tx_count {
                    if rng.gen_bool(0.5) {
                        transactions.push(generate_legacy_transaction());
                    } else {
                        transactions.push(generate_v0_transaction());
                    }
                }
                Entry {
                    num_hashes: rng.gen_range(1..1000),
                    hash: generate_random_hash(),
                    transactions,
                }
            },
            _ => unreachable!(),
        };
        entries.push(entry);
    }
    
    entries
}

fn generate_random_hash() -> Hash {
    let mut rng = rand::thread_rng();
    let mut bytes = [0u8; 32];
    rng.fill(&mut bytes);
    Hash::new_from_array(bytes)
}

fn generate_random_pubkey() -> Pubkey {
    let mut rng = rand::thread_rng();
    let mut bytes = [0u8; 32];
    rng.fill(&mut bytes);
    Pubkey::new_from_array(bytes)
}

fn generate_random_signature() -> Signature {
    let mut rng = rand::thread_rng();
    let mut bytes = [0u8; 64];
    rng.fill(&mut bytes);
    Signature::from(bytes)
}

fn generate_legacy_transaction() -> VersionedTransaction {
    let mut rng = rand::thread_rng();
    
    // Generate signatures
    let num_signatures = rng.gen_range(1..4);
    let signatures: Vec<Signature> = (0..num_signatures)
        .map(|_| generate_random_signature())
        .collect();
    
    // Generate message
    let message = generate_legacy_message();
    
    VersionedTransaction {
        signatures,
        message: VersionedMessage::Legacy(message),
    }
}

fn generate_v0_transaction() -> VersionedTransaction {
    let mut rng = rand::thread_rng();
    
    // Generate signatures
    let num_signatures = rng.gen_range(1..4);
    let signatures: Vec<Signature> = (0..num_signatures)
        .map(|_| generate_random_signature())
        .collect();
    
    // Generate V0 message
    let message = generate_v0_message();
    
    VersionedTransaction {
        signatures,
        message: VersionedMessage::V0(message),
    }
}

fn generate_legacy_message() -> Message {
    let mut rng = rand::thread_rng();
    
    let header = MessageHeader {
        num_required_signatures: rng.gen_range(1..4) as u8,
        num_readonly_signed_accounts: rng.gen_range(0..2) as u8,
        num_readonly_unsigned_accounts: rng.gen_range(0..3) as u8,
    };
    
    let num_accounts = rng.gen_range(3..8);
    let account_keys: Vec<Pubkey> = (0..num_accounts)
        .map(|_| generate_random_pubkey())
        .collect();
    
    let recent_blockhash = generate_random_hash();
    
    let num_instructions = rng.gen_range(1..4);
    let instructions: Vec<CompiledInstruction> = (0..num_instructions)
        .map(|_| generate_compiled_instruction(num_accounts))
        .collect();
    
    Message {
        header,
        account_keys,
        recent_blockhash,
        instructions,
    }
}

fn generate_v0_message() -> V0Message {
    let mut rng = rand::thread_rng();
    
    let header = MessageHeader {
        num_required_signatures: rng.gen_range(1..4) as u8,
        num_readonly_signed_accounts: rng.gen_range(0..2) as u8,
        num_readonly_unsigned_accounts: rng.gen_range(0..3) as u8,
    };
    
    let num_accounts = rng.gen_range(3..8);
    let account_keys: Vec<Pubkey> = (0..num_accounts)
        .map(|_| generate_random_pubkey())
        .collect();
    
    let recent_blockhash = generate_random_hash();
    
    let num_instructions = rng.gen_range(1..4);
    let instructions: Vec<CompiledInstruction> = (0..num_instructions)
        .map(|_| generate_compiled_instruction(num_accounts))
        .collect();
    
    let num_lookups = rng.gen_range(0..3);
    let address_table_lookups: Vec<MessageAddressTableLookup> = (0..num_lookups)
        .map(|_| generate_address_table_lookup())
        .collect();
    
    V0Message {
        header,
        account_keys,
        recent_blockhash,
        instructions,
        address_table_lookups,
    }
}

fn generate_compiled_instruction(max_account_index: usize) -> CompiledInstruction {
    let mut rng = rand::thread_rng();
    
    let program_id_index = rng.gen_range(0..max_account_index) as u8;
    
    let num_accounts = rng.gen_range(0..4);
    let accounts: Vec<u8> = (0..num_accounts)
        .map(|_| rng.gen_range(0..max_account_index) as u8)
        .collect();
    
    let data_len = rng.gen_range(0..100);
    let data: Vec<u8> = (0..data_len)
        .map(|_| rng.gen())
        .collect();
    
    CompiledInstruction {
        program_id_index,
        accounts,
        data,
    }
}

fn generate_address_table_lookup() -> MessageAddressTableLookup {
    let mut rng = rand::thread_rng();
    
    let account_key = generate_random_pubkey();
    
    let writable_count = rng.gen_range(0..5);
    let writable_indexes: Vec<u8> = (0..writable_count)
        .map(|_| rng.gen_range(0..255))
        .collect();
    
    let readonly_count = rng.gen_range(0..5);
    let readonly_indexes: Vec<u8> = (0..readonly_count)
        .map(|_| rng.gen_range(0..255))
        .collect();
    
    MessageAddressTableLookup {
        account_key,
        writable_indexes,
        readonly_indexes,
    }
}
